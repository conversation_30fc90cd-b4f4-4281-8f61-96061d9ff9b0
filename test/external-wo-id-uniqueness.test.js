const { z } = require('zod');
const { createWorkOrderRequestSchemaAPI } = require('../node-server/controllers/schemas/client');

// Mock the database query function
jest.mock('../node-server/utils/db', () => ({
  awaitSafeQuery: jest.fn()
}));

const { awaitSafeQuery } = require('../node-server/utils/db');

describe('External Work Order ID Uniqueness Validation', () => {
  const clientVendorId = 123;
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should allow creation when external_wo_id is unique', async () => {
    // Mock database to return no existing work orders
    awaitSafeQuery.mockResolvedValue([]);

    const schema = createWorkOrderRequestSchemaAPI(clientVendorId);
    const validData = {
      sitefotos_internal_vendor_id: "vendor123",
      sitefotos_internal_site_id: "site456", 
      work_order_details: {
        external_wo_id: "WO-UNIQUE-001",
        trade_id: 1,
        services: [{ service_name: "Test Service", service_type_id: 1 }]
      }
    };

    await expect(schema.parseAsync(validData)).resolves.toBeDefined();
  });

  test('should reject creation when external_wo_id already exists', async () => {
    // Mock database to return existing work order
    awaitSafeQuery.mockResolvedValue([{ swo_id: 999 }]);

    const schema = createWorkOrderRequestSchemaAPI(clientVendorId);
    const duplicateData = {
      sitefotos_internal_vendor_id: "vendor123",
      sitefotos_internal_site_id: "site456", 
      work_order_details: {
        external_wo_id: "WO-DUPLICATE-001",
        trade_id: 1,
        services: [{ service_name: "Test Service", service_type_id: 1 }]
      }
    };

    await expect(schema.parseAsync(duplicateData)).rejects.toThrow();
  });

  test('should call database with correct parameters for uniqueness check', async () => {
    awaitSafeQuery.mockResolvedValue([]);

    const schema = createWorkOrderRequestSchemaAPI(clientVendorId);
    const testData = {
      sitefotos_internal_vendor_id: "vendor123",
      sitefotos_internal_site_id: "site456", 
      work_order_details: {
        external_wo_id: "WO-TEST-001",
        trade_id: 1,
        services: [{ service_name: "Test Service", service_type_id: 1 }]
      }
    };

    await schema.parseAsync(testData);

    // Verify the database query was called with correct parameters
    expect(awaitSafeQuery).toHaveBeenCalledWith(
      expect.stringContaining('swo_external_id = ? AND swo_client_vendor_id = ?'),
      ["WO-TEST-001", clientVendorId]
    );
  });

  test('should allow empty external_wo_id', async () => {
    const schema = createWorkOrderRequestSchemaAPI(clientVendorId);
    const dataWithoutExternalId = {
      sitefotos_internal_vendor_id: "vendor123",
      sitefotos_internal_site_id: "site456", 
      work_order_details: {
        external_wo_id: "",
        trade_id: 1,
        services: [{ service_name: "Test Service", service_type_id: 1 }]
      }
    };

    // Should not call database for empty external_wo_id
    await expect(schema.parseAsync(dataWithoutExternalId)).resolves.toBeDefined();
    expect(awaitSafeQuery).not.toHaveBeenCalledWith(
      expect.stringContaining('swo_external_id'),
      expect.anything()
    );
  });
});
