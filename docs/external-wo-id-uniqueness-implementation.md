# External Work Order ID Uniqueness Implementation

## Problem Statement

The current schema and implementation does not ensure uniqueness of external work order IDs (`external_wo_id`) when creating work orders through the client API. This could lead to:

- Duplicate external work order IDs within the same client vendor
- Data integrity issues
- Confusion when referencing work orders by external ID

## Current State Analysis

### Database Schema
- The `swo_external_id` field in `sitefotos_work_orders` table has no unique constraint
- Field is defined as `varchar(255) DEFAULT NULL`

### Validation
- No uniqueness validation in `createWorkOrderDetailsSchema`
- `saveWorkOrder` function directly inserts without checking for duplicates
- Only basic string validation exists for `external_wo_id`

## Solution Implementation

### 1. Schema Validation (Application Level)

**File**: `node-server/controllers/schemas/client.js`

Added new validation function:
```javascript
const validateExternalWorkOrderIdUniqueness = async (clientVendorId, externalWoId) => {
  if (!externalWoId) return true;
  const query = `
    SELECT wo.swo_id
    FROM sitefotos_work_orders wo
    WHERE wo.swo_external_id = ? AND wo.swo_client_vendor_id = ? AND wo.swo_active = 1;
  `;
  const result = await awaitSafeQuery(query, [externalWoId, clientVendorId]);
  return result && result.length === 0;
};
```

**Enhanced Validation**:
- Added uniqueness check in `validateCreateWorkOrderRequestSchema`
- Added uniqueness check in update work order schema
- Scoped to client vendor (allows same external ID across different vendors)
- Only checks active work orders (`swo_active = 1`)

### 2. Database Constraint (Database Level)

**Files**: 
- `migrations/sqls/20250804000000-add-unique-external-wo-id-up.sql`
- `migrations/sqls/20250804000000-add-unique-external-wo-id-down.sql`

**Migration Features**:
- Handles existing duplicates by appending work order ID as suffix
- Adds unique constraint: `uk_swo_external_id_client_vendor`
- Constraint on combination: `(swo_external_id, swo_client_vendor_id)`

### 3. Test Coverage

**File**: `test/external-wo-id-uniqueness.test.js`

**Test Cases**:
- Allows creation when external_wo_id is unique
- Rejects creation when external_wo_id already exists
- Verifies correct database query parameters
- Allows empty external_wo_id (optional field)

## Benefits

### Data Integrity
- Prevents duplicate external work order IDs within same client vendor
- Maintains referential integrity for external systems
- Database-level constraint as final safety net

### API Reliability
- Clear error messages for duplicate external IDs
- Consistent validation across create and update operations
- Early validation prevents database constraint violations

### Multi-Tenant Support
- Different client vendors can use same external IDs
- Proper scoping prevents cross-vendor conflicts

## Usage Examples

### Successful Creation
```json
{
  "sitefotos_internal_vendor_id": "vendor123",
  "sitefotos_internal_site_id": "site456",
  "work_order_details": {
    "external_wo_id": "WO-UNIQUE-001",
    "trade_id": 1,
    "services": [{"service_name": "Test", "service_type_id": 1}]
  }
}
```

### Duplicate Rejection
```json
{
  "error": [
    {
      "code": "custom",
      "message": "External work order ID already exists for this client vendor",
      "path": ["work_order_details", "external_wo_id"]
    }
  ]
}
```

## Migration Considerations

### Before Migration
- Review existing data for duplicates
- Consider impact on external integrations
- Plan rollback strategy if needed

### After Migration
- Monitor API responses for validation errors
- Update client documentation
- Consider adding logging for duplicate attempts

## Future Enhancements

### Potential Improvements
- Add soft delete support in uniqueness check
- Consider case-insensitive uniqueness
- Add audit logging for duplicate attempts
- Implement bulk validation for batch operations

### Performance Considerations
- Index on `(swo_external_id, swo_client_vendor_id)` for fast lookups
- Consider caching for frequently accessed external IDs
- Monitor query performance with large datasets
