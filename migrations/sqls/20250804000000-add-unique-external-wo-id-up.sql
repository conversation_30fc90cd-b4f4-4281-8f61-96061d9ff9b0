-- Add unique constraint for external work order ID per client vendor
-- This ensures that each client vendor can only have one work order with a specific external_wo_id

-- First, let's handle any existing duplicates by updating them with a suffix
-- This is a safety measure in case there are already duplicate external IDs in the system

UPDATE sitefotos_work_orders swo1
SET swo_external_id = CONCAT(swo_external_id, '_', swo1.swo_id)
WHERE swo1.swo_external_id IS NOT NULL 
  AND swo1.swo_client_vendor_id IS NOT NULL
  AND EXISTS (
    SELECT 1 
    FROM sitefotos_work_orders swo2 
    WHERE swo2.swo_external_id = swo1.swo_external_id 
      AND swo2.swo_client_vendor_id = swo1.swo_client_vendor_id 
      AND swo2.swo_id != swo1.swo_id
      AND swo2.swo_active = 1
  )
  AND swo1.swo_active = 1;

-- Add unique constraint on combination of external_wo_id and client_vendor_id
-- This allows the same external_wo_id to be used by different client vendors
-- but prevents duplicates within the same client vendor
ALTER TABLE sitefotos_work_orders 
ADD CONSTRAINT uk_swo_external_id_client_vendor 
UNIQUE (swo_external_id, swo_client_vendor_id);
